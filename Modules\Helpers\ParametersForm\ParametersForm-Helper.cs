// ParametersForm Helper - Helper methods for Parameters form operations
// Usage: Provides utility methods for grid management, data binding, and form operations

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Data.ParametersForm;
using ProManage.Modules.Models.ParametersForm;
using ProManage.Modules.Services;

namespace ProManage.Modules.Helpers.ParametersForm
{
    /// <summary>
    /// Helper class for Parameters form operations
    /// </summary>
    public static class ParametersFormHelper
    {
        /// <summary>
        /// Initializes the parameters grid with proper columns and data binding
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void InitializeGrid(dynamic form)
        {
            try
            {
                var gridControl = form.gridControl1;
                var gridView = form.gridView1 as GridView;

                // Create DataTable for grid binding
                var gridDataTable = new DataTable();
                gridDataTable.Columns.Add("Id", typeof(int));
                gridDataTable.Columns.Add("ParameterCode", typeof(string));
                gridDataTable.Columns.Add("ParameterType", typeof(string));
                gridDataTable.Columns.Add("ParameterValue", typeof(string));
                gridDataTable.Columns.Add("Purpose", typeof(string));
                gridDataTable.Columns.Add("CreatedAt", typeof(DateTime));
                gridDataTable.Columns.Add("ModifiedAt", typeof(DateTime));
                gridDataTable.Columns.Add("Selected", typeof(bool));

                // Set the DataTable as the grid's data source FIRST
                form.GridDataTable = gridDataTable;
                gridControl.DataSource = gridDataTable;

                // Clear any existing columns
                gridView.Columns.Clear();

                // Add columns directly to ensure they're visible
                var colId = gridView.Columns.AddVisible("Id", "ID");
                colId.Visible = false;
                colId.OptionsColumn.AllowEdit = false;

                var colCode = gridView.Columns.AddVisible("ParameterCode", "Parameter Code");
                colCode.Width = 150;
                colCode.OptionsColumn.AllowEdit = false;

                var colType = gridView.Columns.AddVisible("ParameterType", "Type");
                colType.Width = 80;
                colType.OptionsColumn.AllowEdit = false;

                var colValue = gridView.Columns.AddVisible("ParameterValue", "Parameter Value");
                colValue.Width = 200;
                colValue.OptionsColumn.AllowEdit = false;

                var colPurpose = gridView.Columns.AddVisible("Purpose", "Purpose");
                colPurpose.Width = 250;
                colPurpose.OptionsColumn.AllowEdit = false;

                var colCreated = gridView.Columns.AddVisible("CreatedAt", "Created");
                colCreated.Width = 120;
                colCreated.OptionsColumn.AllowEdit = false;
                colCreated.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colCreated.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm";

                var colModified = gridView.Columns.AddVisible("ModifiedAt", "Modified");
                colModified.Width = 120;
                colModified.OptionsColumn.AllowEdit = false;
                colModified.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colModified.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm";

                var colSelected = gridView.Columns.AddVisible("Selected", "Select");
                colSelected.Width = 60;
                colSelected.OptionsColumn.AllowEdit = true;
                colSelected.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();

                // Configure grid view options - Only Select column is editable
                gridView.OptionsView.ShowColumnHeaders = true;
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ShowIndicator = true;
                gridView.OptionsBehavior.Editable = true; // Enable editing for Select column only
                gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
                gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
                gridView.OptionsNavigation.EnterMoveNextColumn = true;

                // Setup grid events for single row selection
                SetupGridEvents(form);

                // Force refresh
                gridView.RefreshData();
                gridControl.Refresh();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing grid: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Configures the grid columns with proper settings
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        private static void ConfigureGridColumns(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== ConfigureGridColumns: Starting ===");

                var gridView = form.gridView1 as GridView;
                if (gridView == null)
                {
                    throw new Exception("GridView is null in ConfigureGridColumns");
                }

                Debug.WriteLine("GridView obtained, clearing existing columns...");

                // Clear existing columns
                gridView.Columns.Clear();
                Debug.WriteLine($"Columns cleared. Current column count: {gridView.Columns.Count}");

                // Add columns with proper configuration
                Debug.WriteLine("Adding ID column...");
                var colId = gridView.Columns.AddField("Id");
                colId.Caption = "ID";
                colId.Visible = false; // Hide ID column
                colId.OptionsColumn.AllowEdit = false;
                Debug.WriteLine("ID column added");

                Debug.WriteLine("Adding Parameter Code column...");
                var colCode = gridView.Columns.AddField("ParameterCode");
                colCode.Caption = "Parameter Code";
                colCode.Width = 150;
                colCode.OptionsColumn.AllowEdit = false;
                Debug.WriteLine("Parameter Code column added");

                Debug.WriteLine("Adding Parameter Type column...");
                var colType = gridView.Columns.AddField("ParameterType");
                colType.Caption = "Type";
                colType.Width = 80;
                colType.OptionsColumn.AllowEdit = false;
                Debug.WriteLine("Parameter Type column added");

                Debug.WriteLine("Adding Parameter Value column...");
                var colValue = gridView.Columns.AddField("ParameterValue");
                colValue.Caption = "Parameter Value";
                colValue.Width = 200;
                colValue.OptionsColumn.AllowEdit = false;
                Debug.WriteLine("Parameter Value column added");

                Debug.WriteLine("Adding Purpose column...");
                var colPurpose = gridView.Columns.AddField("Purpose");
                colPurpose.Caption = "Purpose";
                colPurpose.Width = 250;
                colPurpose.OptionsColumn.AllowEdit = false;
                Debug.WriteLine("Purpose column added");

                Debug.WriteLine("Adding Created column...");
                var colCreated = gridView.Columns.AddField("CreatedAt");
                colCreated.Caption = "Created";
                colCreated.Width = 120;
                colCreated.OptionsColumn.AllowEdit = false;
                colCreated.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colCreated.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm";
                Debug.WriteLine("Created column added");

                Debug.WriteLine("Adding Modified column...");
                var colModified = gridView.Columns.AddField("ModifiedAt");
                colModified.Caption = "Modified";
                colModified.Width = 120;
                colModified.OptionsColumn.AllowEdit = false;
                colModified.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colModified.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm";
                Debug.WriteLine("Modified column added");

                // Add checkbox column for selection (rightmost) - ONLY editable column
                Debug.WriteLine("Adding Select checkbox column...");
                var colSelected = gridView.Columns.AddField("Selected");
                colSelected.Caption = "Select";
                colSelected.Width = 60;
                colSelected.OptionsColumn.AllowEdit = true;
                colSelected.ColumnEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
                Debug.WriteLine("Select column added");

                // Configure grid behavior - Only Select column is editable
                Debug.WriteLine("Configuring grid behavior...");
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.None;
                gridView.OptionsBehavior.Editable = true; // Enable editing for Select column only
                gridView.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
                gridView.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
                gridView.OptionsNavigation.EnterMoveNextColumn = true; // Better navigation
                gridView.OptionsView.ShowIndicator = true; // Show row indicators

                // Ensure column headers are visible
                Debug.WriteLine("Setting column header visibility...");
                gridView.OptionsView.ShowColumnHeaders = true;
                gridView.OptionsView.ColumnAutoWidth = false;

                Debug.WriteLine($"=== ConfigureGridColumns: Completed Successfully. Total columns: {gridView.Columns.Count} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in ConfigureGridColumns: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Loads all parameters from database and displays them in the grid
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void LoadParametersToGrid(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== LoadParametersToGrid: Starting ===");

                // Clear existing data
                var gridDataTable = form.GridDataTable as DataTable;
                gridDataTable.Clear();

                // Get parameters from repository
                var parameters = ParametersFormRepository.GetAllParameters();
                Debug.WriteLine($"Retrieved {parameters.Count} parameters from repository");

                // Add parameters to grid
                foreach (var parameter in parameters)
                {
                    try
                    {
                        var row = gridDataTable.NewRow();
                        row["Id"] = parameter.Id;
                        row["ParameterCode"] = parameter.ParameterCode ?? "";

                        // Safely get parameter type display name with fallback
                        string parameterTypeDisplay;
                        try
                        {
                            parameterTypeDisplay = ParameterTypeHelper.GetDisplayName(parameter.ParameterType);
                        }
                        catch (Exception typeEx)
                        {
                            Debug.WriteLine($"Error getting display name for parameter type {parameter.ParameterType}: {typeEx.Message}");
                            parameterTypeDisplay = "String"; // Default fallback
                        }

                        row["ParameterType"] = parameterTypeDisplay;
                        row["ParameterValue"] = parameter.ParameterValue ?? "";
                        row["Purpose"] = parameter.Purpose ?? "";
                        row["CreatedAt"] = parameter.CreatedAt;
                        row["ModifiedAt"] = parameter.ModifiedAt ?? (object)DBNull.Value;
                        row["Selected"] = false; // Default to not selected

                        gridDataTable.Rows.Add(row);
                    }
                    catch (Exception paramEx)
                    {
                        Debug.WriteLine($"Error adding parameter {parameter.ParameterCode} to grid: {paramEx.Message}");
                        // Continue with next parameter instead of failing completely
                    }
                }

                // Refresh grid display
                RefreshGridDisplay(form);

                // Ensure headers are visible after loading data
                EnsureGridColumnsVisible(form);

                Debug.WriteLine($"=== LoadParametersToGrid: Loaded {parameters.Count} parameters ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadParametersToGrid: {ex.Message}");
                MessageBox.Show($"Error loading parameters: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Refreshes the grid display
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void RefreshGridDisplay(dynamic form)
        {
            try
            {
                form.gridControl1.RefreshDataSource();
                form.gridView1.RefreshData();
                form.gridView1.LayoutChanged();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RefreshGridDisplay: {ex.Message}");
            }
        }

        /// <summary>
        /// Ensures grid columns and headers are visible
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void EnsureGridColumnsVisible(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== EnsureGridColumnsVisible: Starting ===");

                var gridView = form.gridView1 as GridView;
                var gridControl = form.gridControl1;
                var gridDataTable = form.GridDataTable as DataTable;

                if (gridView == null || gridControl == null)
                {
                    Debug.WriteLine("ERROR: GridView or GridControl is null");
                    return;
                }

                // Force column headers to be visible
                gridView.OptionsView.ShowColumnHeaders = true;

                // Best fit columns to ensure proper display
                gridView.BestFitColumns();

                // Add a dummy row to make columns visible if grid is empty, then remove it
                if (gridDataTable != null && gridDataTable.Rows.Count == 0)
                {
                    Debug.WriteLine("Adding dummy row to make columns visible");
                    var dummyRow = gridDataTable.NewRow();
                    dummyRow["Id"] = 0;
                    dummyRow["ParameterCode"] = "";
                    dummyRow["ParameterType"] = "";
                    dummyRow["ParameterValue"] = "";
                    dummyRow["Purpose"] = "";
                    dummyRow["CreatedAt"] = DBNull.Value; // Consistent with new row creation
                    dummyRow["ModifiedAt"] = DBNull.Value;
                    dummyRow["Selected"] = false;

                    gridDataTable.Rows.Add(dummyRow);
                    RefreshGridDisplay(form);
                    gridDataTable.Rows.Clear();

                    Debug.WriteLine("Grid columns made visible with dummy row technique");
                }

                // Force refresh and ensure headers are visible
                gridView.OptionsView.ShowColumnHeaders = true;
                gridView.Invalidate();
                gridControl.Refresh();

                Debug.WriteLine("=== EnsureGridColumnsVisible: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error ensuring grid columns visible: {ex.Message}");
            }
        }

        /// <summary>
        /// Adds a new empty row to the grid for parameter entry
        /// NOTE: This method is deprecated - use ParamEntryForm popup instead
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        [Obsolete("This method is deprecated. Use ParamEntryForm popup dialog instead.")]
        public static void AddNewParameterRow(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== AddNewParameterRow: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                var gridView = form.gridView1 as GridView;

                if (gridDataTable == null)
                {
                    Debug.WriteLine("ERROR: GridDataTable is null");
                    MessageBox.Show("Grid is not properly initialized.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (gridView == null)
                {
                    Debug.WriteLine("ERROR: GridView is null");
                    MessageBox.Show("Grid view is not properly initialized.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                Debug.WriteLine($"Current row count before adding: {gridDataTable.Rows.Count}");

                // Create new row
                var newRow = gridDataTable.NewRow();
                newRow["Id"] = 0; // 0 indicates new record
                newRow["ParameterCode"] = "";
                newRow["ParameterType"] = ParameterTypeHelper.GetDisplayName(ParameterType.String); // Default to String
                newRow["ParameterValue"] = "";
                newRow["Purpose"] = "";
                newRow["CreatedAt"] = DBNull.Value; // Do not set created_at until save
                newRow["ModifiedAt"] = DBNull.Value;
                newRow["Selected"] = false;

                // Add row to table
                gridDataTable.Rows.Add(newRow);
                Debug.WriteLine($"Row added. New row count: {gridDataTable.Rows.Count}");

                // Refresh grid display
                RefreshGridDisplay(form);

                // Focus on the new row with proper DevExpress methods
                try
                {
                    // Get the last row handle (DevExpress uses handles, not indices)
                    int lastRowHandle = gridView.GetRowHandle(gridDataTable.Rows.Count - 1);
                    Debug.WriteLine($"Attempting to focus on row handle: {lastRowHandle}");

                    // Set focus to the new row
                    gridView.FocusedRowHandle = lastRowHandle;

                    // Find the ParameterCode column and focus on it
                    var parameterCodeColumn = gridView.Columns.ColumnByFieldName("ParameterCode");
                    if (parameterCodeColumn != null)
                    {
                        gridView.FocusedColumn = parameterCodeColumn;
                        Debug.WriteLine("Focused on ParameterCode column");
                    }
                    else
                    {
                        Debug.WriteLine("WARNING: ParameterCode column not found");
                    }

                    // Start editing the cell
                    gridView.ShowEditor();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Could not focus on new row: {ex.Message}");
                }

                Debug.WriteLine("=== AddNewParameterRow: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in AddNewParameterRow: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error adding new parameter row: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Gets the selected parameter from the grid for editing
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        /// <returns>The selected parameter model, or null if none/multiple selected</returns>
        public static ParametersFormModel GetSelectedParameter(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== GetSelectedParameter: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                var gridView = form.gridView1 as GridView;

                if (gridDataTable == null || gridDataTable.Rows.Count == 0)
                {
                    Debug.WriteLine("No data in grid");
                    return null;
                }

                if (gridView == null)
                {
                    Debug.WriteLine("GridView is null");
                    return null;
                }

                // First, try to get selection from GridView directly (more reliable for DevExpress)
                var selectedRows = new List<DataRow>();

                // Check both DataTable and GridView for selected rows
                Debug.WriteLine($"Checking {gridDataTable.Rows.Count} rows in DataTable");
                for (int i = 0; i < gridDataTable.Rows.Count; i++)
                {
                    var row = gridDataTable.Rows[i];
                    bool isSelectedInDataTable = Convert.ToBoolean(row["Selected"]);

                    // Also check the GridView value
                    int rowHandle = gridView.GetRowHandle(i);
                    object gridViewValue = gridView.GetRowCellValue(rowHandle, "Selected");
                    bool isSelectedInGridView = gridViewValue != null && Convert.ToBoolean(gridViewValue);

                    Debug.WriteLine($"Row {i}: DataTable Selected={isSelectedInDataTable}, GridView Selected={isSelectedInGridView}, ParameterCode={row["ParameterCode"]}");

                    // Use GridView value as it's more current
                    if (isSelectedInGridView)
                    {
                        selectedRows.Add(row);
                        Debug.WriteLine($"Found selected row: {row["ParameterCode"]}");
                    }
                }

                if (selectedRows.Count == 0)
                {
                    Debug.WriteLine("No rows selected - checking if there's a focused row as fallback");

                    // As a fallback, check if there's a focused row with checkbox checked
                    int focusedRowHandle = gridView.FocusedRowHandle;
                    if (focusedRowHandle >= 0)
                    {
                        object focusedValue = gridView.GetRowCellValue(focusedRowHandle, "Selected");
                        bool isFocusedSelected = focusedValue != null && Convert.ToBoolean(focusedValue);
                        Debug.WriteLine($"Focused row handle {focusedRowHandle} has Selected={isFocusedSelected}");

                        if (isFocusedSelected)
                        {
                            int dataRowIndex = gridView.GetDataSourceRowIndex(focusedRowHandle);
                            if (dataRowIndex >= 0 && dataRowIndex < gridDataTable.Rows.Count)
                            {
                                selectedRows.Add(gridDataTable.Rows[dataRowIndex]);
                                Debug.WriteLine($"Using focused row as selected: {gridDataTable.Rows[dataRowIndex]["ParameterCode"]}");
                            }
                        }
                    }
                }

                if (selectedRows.Count == 0)
                {
                    Debug.WriteLine("No rows selected after all checks");
                    return null;
                }

                if (selectedRows.Count > 1)
                {
                    Debug.WriteLine($"Multiple rows selected ({selectedRows.Count}), edit requires single selection");
                    return null;
                }

                // Get the selected parameter data
                var selectedRow = selectedRows[0];
                var parameter = new ParametersFormModel
                {
                    Id = Convert.ToInt32(selectedRow["Id"]),
                    ParameterCode = selectedRow["ParameterCode"]?.ToString() ?? "",
                    ParameterValue = selectedRow["ParameterValue"]?.ToString() ?? "",
                    Purpose = selectedRow["Purpose"]?.ToString(),
                    CreatedAt = Convert.ToDateTime(selectedRow["CreatedAt"]),
                    ModifiedAt = selectedRow["ModifiedAt"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(selectedRow["ModifiedAt"])
                };

                Debug.WriteLine($"=== GetSelectedParameter: Found parameter ID {parameter.Id} - {parameter.ParameterCode} ===");
                return parameter;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetSelectedParameter: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// Deletes selected parameters from the database and grid
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void DeleteSelectedParameters(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== DeleteSelectedParameters: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable == null || gridDataTable.Rows.Count == 0)
                {
                    MessageBox.Show("No parameters to delete.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Get selected parameter IDs
                var selectedIds = new List<int>();
                var rowsToRemove = new List<DataRow>();

                foreach (DataRow row in gridDataTable.Rows)
                {
                    bool isSelected = Convert.ToBoolean(row["Selected"]);
                    if (isSelected)
                    {
                        int id = Convert.ToInt32(row["Id"]);
                        if (id > 0) // Only delete existing records (not new ones)
                        {
                            selectedIds.Add(id);
                        }
                        rowsToRemove.Add(row);
                    }
                }

                if (selectedIds.Count == 0 && rowsToRemove.Count == 0)
                {
                    MessageBox.Show("No parameters selected for deletion.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Confirm deletion
                string message = selectedIds.Count > 0
                    ? $"Are you sure you want to delete {selectedIds.Count} parameter(s) from the database?"
                    : $"Are you sure you want to remove {rowsToRemove.Count} new parameter row(s)?";

                var result = MessageBox.Show(message, "Confirm Delete",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    return;
                }

                // Delete from database if there are existing records
                if (selectedIds.Count > 0)
                {
                    int deletedCount = ParametersFormRepository.DeleteParameters(selectedIds);
                    Debug.WriteLine($"Deleted {deletedCount} parameters from database");

                    // Refresh parameter cache after successful deletion
                    Debug.WriteLine("Refreshing parameter cache after delete operations");
                    bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                    if (cacheRefreshed)
                    {
                        Debug.WriteLine("Parameter cache refreshed successfully");
                    }
                    else
                    {
                        Debug.WriteLine("Warning: Parameter cache refresh failed");
                    }
                }

                // Remove rows from grid
                foreach (var row in rowsToRemove)
                {
                    gridDataTable.Rows.Remove(row);
                }

                // Refresh grid display
                RefreshGridDisplay(form);

                string successMessage = selectedIds.Count > 0
                    ? $"Successfully deleted {selectedIds.Count} parameter(s)."
                    : $"Successfully removed {rowsToRemove.Count} new parameter row(s).";

                MessageBox.Show(successMessage, "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                Debug.WriteLine("=== DeleteSelectedParameters: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteSelectedParameters: {ex.Message}");
                MessageBox.Show($"Error deleting parameters: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Validates grid data for mandatory fields
        /// </summary>
        /// <param name="gridDataTable">The grid data table to validate</param>
        /// <returns>List of validation error messages</returns>
        private static List<string> ValidateGridData(DataTable gridDataTable)
        {
            var errors = new List<string>();
            int rowNumber = 1;

            foreach (DataRow row in gridDataTable.Rows)
            {
                // Skip selected rows (they will be deleted)
                if (Convert.ToBoolean(row["Selected"]))
                {
                    rowNumber++;
                    continue;
                }

                string paramCode = row["ParameterCode"]?.ToString()?.Trim();
                string paramValue = row["ParameterValue"]?.ToString()?.Trim();
                string purpose = row["Purpose"]?.ToString()?.Trim();

                // Check if row has any data (not completely empty)
                bool hasAnyData = !string.IsNullOrWhiteSpace(paramCode) ||
                                 !string.IsNullOrWhiteSpace(paramValue) ||
                                 !string.IsNullOrWhiteSpace(purpose);

                if (hasAnyData)
                {
                    // If row has any data, both parameter_code and parameter_value are mandatory
                    if (string.IsNullOrWhiteSpace(paramCode))
                    {
                        errors.Add($"Row {rowNumber}: Parameter Code is required");
                    }

                    if (string.IsNullOrWhiteSpace(paramValue))
                    {
                        errors.Add($"Row {rowNumber}: Parameter Value is required");
                    }
                }

                rowNumber++;
            }

            return errors;
        }

        /// <summary>
        /// Saves all changes in the grid to the database
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void SaveParameters(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== SaveParameters: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable == null)
                {
                    MessageBox.Show("Grid is not properly initialized.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Validate grid data before saving
                var validationErrors = ValidateGridData(gridDataTable);
                if (validationErrors.Count > 0)
                {
                    string errorMessage = "Please correct the following errors before saving:\n\n" +
                                        string.Join("\n", validationErrors);
                    MessageBox.Show(errorMessage, "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int savedCount = 0;
                int updatedCount = 0;

                foreach (DataRow row in gridDataTable.Rows)
                {
                    // Skip selected rows (they will be deleted)
                    if (Convert.ToBoolean(row["Selected"]))
                        continue;

                    // Skip completely empty rows
                    string paramCode = row["ParameterCode"]?.ToString()?.Trim();
                    string paramValue = row["ParameterValue"]?.ToString()?.Trim();
                    string paramTypeDisplay = row["ParameterType"]?.ToString()?.Trim();
                    string purpose = row["Purpose"]?.ToString()?.Trim();

                    if (string.IsNullOrWhiteSpace(paramCode) &&
                        string.IsNullOrWhiteSpace(paramValue) &&
                        string.IsNullOrWhiteSpace(purpose))
                        continue;

                    // Convert parameter type display name back to enum
                    ParameterType parameterType = GetParameterTypeFromDisplayName(paramTypeDisplay);

                    var parameter = new ParametersFormModel
                    {
                        Id = Convert.ToInt32(row["Id"]),
                        ParameterCode = paramCode,
                        ParameterValue = paramValue,
                        ParameterType = parameterType,
                        Purpose = purpose,
                        CreatedAt = row["CreatedAt"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["CreatedAt"]),
                        ModifiedAt = row["ModifiedAt"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(row["ModifiedAt"])
                    };

                    if (parameter.Id == 0) // New parameter
                    {
                        // Set created_at timestamp only when saving new records
                        parameter.CreatedAt = DateTime.Now;
                        int newId = ParametersFormRepository.InsertParameter(parameter);
                        row["Id"] = newId; // Update the row with the new ID
                        row["CreatedAt"] = parameter.CreatedAt; // Update grid with actual timestamp
                        savedCount++;
                    }
                    else // Existing parameter
                    {
                        // Keep existing created_at, only update modified_at
                        parameter.ModifiedAt = DateTime.Now;
                        ParametersFormRepository.UpdateParameter(parameter);
                        row["ModifiedAt"] = parameter.ModifiedAt;
                        updatedCount++;
                    }
                }

                // Refresh grid display
                RefreshGridDisplay(form);

                // Refresh parameter cache if any parameters were saved or updated
                if (savedCount > 0 || updatedCount > 0)
                {
                    Debug.WriteLine("Refreshing parameter cache after save operations");
                    bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                    if (cacheRefreshed)
                    {
                        Debug.WriteLine("Parameter cache refreshed successfully");
                    }
                    else
                    {
                        Debug.WriteLine("Warning: Parameter cache refresh failed");
                    }

                    MessageBox.Show($"Successfully saved {savedCount} new parameter(s) and updated {updatedCount} existing parameter(s).",
                        "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("No changes to save.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                Debug.WriteLine($"=== SaveParameters: Saved {savedCount}, Updated {updatedCount} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SaveParameters: {ex.Message}");
                MessageBox.Show($"Error saving parameters: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Refreshes the parameters grid by reloading data from the database
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        public static void RefreshParametersGrid(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== RefreshParametersGrid: Starting ===");

                // Clear existing data
                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable != null)
                {
                    gridDataTable.Clear();
                    Debug.WriteLine("Existing grid data cleared");
                }

                // Reload parameters from database
                LoadParametersToGrid(form);

                Debug.WriteLine("=== RefreshParametersGrid: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RefreshParametersGrid: {ex.Message}");
                MessageBox.Show($"Error refreshing parameters grid: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up grid events for single row selection functionality
        /// </summary>
        /// <param name="form">The Parameters form instance</param>
        private static void SetupGridEvents(dynamic form)
        {
            try
            {
                var gridView = form.gridView1 as GridView;
                if (gridView == null)
                {
                    Debug.WriteLine("ERROR: GridView is null in SetupGridEvents");
                    return;
                }

                // Remove existing event handlers to avoid duplicates
                gridView.CellValueChanged -= GridView_CellValueChanged;
                gridView.CellValueChanging -= GridView_CellValueChanging;
                gridView.ShowingEditor -= GridView_ShowingEditor;

                // Add event handlers for checkbox single selection
                gridView.ShowingEditor += GridView_ShowingEditor;
                gridView.CellValueChanging += GridView_CellValueChanging;
                gridView.CellValueChanged += GridView_CellValueChanged;

                Debug.WriteLine("Grid events setup completed for single row selection");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up grid events: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles showing editor event to enforce single row selection for checkboxes
        /// </summary>
        /// <param name="sender">The grid view</param>
        /// <param name="e">Event arguments</param>
        private static void GridView_ShowingEditor(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // Only handle the Selected column
                if (gridView.FocusedColumn?.FieldName != "Selected") return;

                Debug.WriteLine($"=== GridView_ShowingEditor: About to edit Selected column in row {gridView.FocusedRowHandle} ===");

                // Get the current value of the checkbox being clicked
                object currentValue = gridView.GetFocusedRowCellValue("Selected");
                bool isCurrentlyChecked = currentValue != null && Convert.ToBoolean(currentValue);

                Debug.WriteLine($"Current checkbox value: {isCurrentlyChecked}");

                // If the checkbox is currently unchecked and about to be checked, uncheck all others first
                if (!isCurrentlyChecked)
                {
                    Debug.WriteLine("Checkbox about to be checked, unchecking all others preemptively");

                    // Temporarily remove event handlers to avoid infinite loop
                    gridView.CellValueChanging -= GridView_CellValueChanging;
                    gridView.CellValueChanged -= GridView_CellValueChanged;

                    try
                    {
                        // Uncheck all other rows
                        for (int i = 0; i < gridView.DataRowCount; i++)
                        {
                            int rowHandle = gridView.GetRowHandle(i);
                            if (rowHandle != gridView.FocusedRowHandle)
                            {
                                object otherValue = gridView.GetRowCellValue(rowHandle, "Selected");
                                bool isOtherChecked = otherValue != null && Convert.ToBoolean(otherValue);

                                if (isOtherChecked)
                                {
                                    gridView.SetRowCellValue(rowHandle, "Selected", false);
                                    Debug.WriteLine($"Preemptively unchecked row {rowHandle}");
                                }
                            }
                        }
                    }
                    finally
                    {
                        // Re-add event handlers
                        gridView.CellValueChanging += GridView_CellValueChanging;
                        gridView.CellValueChanged += GridView_CellValueChanged;
                    }

                    Debug.WriteLine("Preemptive single row selection enforced");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_ShowingEditor: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles cell value changing to enforce single row selection for checkboxes
        /// </summary>
        /// <param name="sender">The grid view</param>
        /// <param name="e">Event arguments</param>
        private static void GridView_CellValueChanging(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // Only handle changes to the Selected column
                if (e.Column.FieldName != "Selected") return;

                Debug.WriteLine($"=== GridView_CellValueChanging: Selected column changing in row {e.RowHandle}, new value={e.Value} ===");

                // If a checkbox is being checked, uncheck all others immediately
                bool isBeingChecked = Convert.ToBoolean(e.Value);
                if (isBeingChecked)
                {
                    Debug.WriteLine($"Checkbox being checked in row {e.RowHandle}, unchecking all others immediately");

                    // Temporarily remove event handlers to avoid infinite loop
                    gridView.CellValueChanging -= GridView_CellValueChanging;
                    gridView.CellValueChanged -= GridView_CellValueChanged;

                    try
                    {
                        // Uncheck all other rows immediately
                        for (int i = 0; i < gridView.DataRowCount; i++)
                        {
                            int rowHandle = gridView.GetRowHandle(i);
                            if (rowHandle != e.RowHandle)
                            {
                                // Get current value to avoid unnecessary updates
                                object currentValue = gridView.GetRowCellValue(rowHandle, "Selected");
                                bool isCurrentlyChecked = currentValue != null && Convert.ToBoolean(currentValue);

                                if (isCurrentlyChecked)
                                {
                                    gridView.SetRowCellValue(rowHandle, "Selected", false);
                                    Debug.WriteLine($"Unchecked row {rowHandle}");
                                }
                            }
                        }
                    }
                    finally
                    {
                        // Re-add event handlers
                        gridView.CellValueChanging += GridView_CellValueChanging;
                        gridView.CellValueChanged += GridView_CellValueChanged;
                    }

                    Debug.WriteLine("Single row selection enforced in CellValueChanging");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CellValueChanging: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles cell value changes to enforce single row selection for checkboxes
        /// </summary>
        /// <param name="sender">The grid view</param>
        /// <param name="e">Event arguments</param>
        private static void GridView_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // Only handle changes to the Selected column
                if (e.Column.FieldName != "Selected") return;

                Debug.WriteLine($"=== GridView_CellValueChanged: Selected column changed in row {e.RowHandle}, value={e.Value} ===");

                // Ensure DataTable is synchronized with GridView
                int dataRowIndex = gridView.GetDataSourceRowIndex(e.RowHandle);
                if (dataRowIndex >= 0)
                {
                    var dataSource = gridView.DataSource as DataTable;
                    if (dataSource != null && dataRowIndex < dataSource.Rows.Count)
                    {
                        bool isChecked = Convert.ToBoolean(e.Value);
                        dataSource.Rows[dataRowIndex]["Selected"] = isChecked;
                        Debug.WriteLine($"Synchronized DataTable: Row {dataRowIndex} Selected={isChecked}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CellValueChanged: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Converts a parameter type display name back to the ParameterType enum
        /// </summary>
        /// <param name="displayName">The display name to convert</param>
        /// <returns>The corresponding ParameterType enum value</returns>
        private static ParameterType GetParameterTypeFromDisplayName(string displayName)
        {
            if (string.IsNullOrWhiteSpace(displayName))
                return ParameterType.String; // Default

            // Get all parameter types and find matching display name
            var allTypes = ParameterTypeHelper.GetAllTypes();
            foreach (var kvp in allTypes)
            {
                if (string.Equals(kvp.Value, displayName, StringComparison.OrdinalIgnoreCase))
                {
                    return kvp.Key;
                }
            }

            // If no match found, default to String
            return ParameterType.String;
        }
    }
}
